import React, { createContext, useContext, useState, useEffect } from "react";
import { AuthContextType, checkAuthValidity, type User } from "../lib/schema";
import { clearAuth, getStoredAuth, storeAuth } from "../lib/auth";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const [authState, setAuthState] = useState(() => getStoredAuth());

  const validateToken = async (refreshToken: string): Promise<checkAuthValidity> => {
    try {
      const res = await fetch(
        `${import.meta.env.VITE_LOGIN_API_BASE_URL}/api/auth/checkAuthValidity`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            refreshToken: refreshToken,
            application: "home",
          }),
        }
      );
      debugger;
      if (res.status === 200) {
        const data = await res.json();
        return data;
      } else {
        return {
          success: false,
          message: "Token is invalid",
          user: null,
          accessToken: null,
          refreshToken: null,
        };
      }
    } catch (error) {
      debugger;
      console.error("Token validation failed:", error);
      return {
        success: false,
        message: "Token is invalid",
        user: null,
        accessToken: null,
        refreshToken: null,
      };
    }
  };

  const clearAuthAndRedirect = async () => {
    await clearAuth();
    setAuthState({
      accessToken: null,
      isAuthenticated: false,
      user: null,
      refreshToken: null,
    });

    window.location.href = `${import.meta.env.VITE_LOGIN_URL
      }/login?redirectUrl=${encodeURIComponent(window.location.href)}`;
  };

  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);

      const urlParams = new URLSearchParams(window.location.search);
      const refreshToken = urlParams.get("refreshToken");

      if (refreshToken) {
        // Clean URL without full page reload
        const url = new URL(window.location.href);
        url.searchParams.delete("refreshToken");
        window.history.replaceState(
          {},
          document.title,
          url.pathname + url.search
        );
        const res = await validateToken(refreshToken);
        if (res.success) {
          await clearAuth();
          await storeAuth(res.user, res.accessToken, res.refreshToken);
          setAuthState(await getStoredAuth());
        } else {
          await clearAuthAndRedirect();
          return;
        }
      } else {
        const stored = getStoredAuth();

        if (stored.refreshToken) {
          const res = await validateToken(stored.refreshToken);
          if (res.success) {
            await clearAuth();
            await storeAuth(res.user, res.accessToken, res.refreshToken);
            setAuthState(stored);
          } else {
            await clearAuthAndRedirect();
            return;
          }
        } else {
          await clearAuthAndRedirect();
          return;
        }
      }

      setIsLoading(false);
    };

    initAuth();
  }, []);

  const logout = async () => {
    setIsLoading(true);
    await clearAuth();
    setAuthState({
      accessToken: null,
      isAuthenticated: false,
      user: null,
      refreshToken: null,
    });
    window.location.href = `${import.meta.env.VITE_LOGIN_URL}?logout=true`;
    setIsLoading(false);
  };

  return React.createElement(
    AuthContext.Provider,
    {
      value: {
        ...authState,
        logout,
        isLoading: isLoading,
      },
    },
    children
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
