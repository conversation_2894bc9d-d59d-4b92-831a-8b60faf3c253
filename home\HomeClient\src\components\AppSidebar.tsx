
import { Settings, Radio } from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { useTranslation } from "react-i18next";

const mainItems = [
  {
    id: "application",
    title: "application",
    icon: Radio,
    description: "Application modules",
  },
  {
    id: "admin",
    title: "admin",
    icon: Settings,
    description: "Administration",
  },
];

interface AppSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function AppSidebar({ activeSection, onSectionChange }: AppSidebarProps) {
  const { t } = useTranslation();

  return (
    <Sidebar className="border-r">
      <SidebarHeader className="p-6">
        <h2 className="text-lg font-semibold text-sidebar-foreground">{t("navigation")}</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    className={`w-full justify-start p-3 hover:bg-sidebar-accent transition-colors ${
                      activeSection === item.id ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''
                    }`}
                    onClick={() => onSectionChange(item.id)}
                  >
                    <item.icon className="h-4 w-4" />
                    <span className="font-medium">{t(item.title)}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
