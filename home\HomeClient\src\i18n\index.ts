import i18n from "i18next";
import { initReactI18next } from "react-i18next";

const resources = {
  en: {
    translation: {
      home_portal: "Home Portal",
      application: "Application",
      admin: "Admin",
      navigation: "Navigation",
      application_dashboard: "Application Dashboard",
      admin_dashboard: "Admin Dashboard",
      application_modules: "Applications",
      administration: "Administration",
      dispatch: "Dispatch",
      dispatch_desc:
        "Manage emergency dispatch operations and resource allocation",
      "911": "911",
      "911_desc": "Handle emergency calls and coordinate response efforts",
      jail: "Jail",
      jail_desc: "Correctional facility management and inmate tracking",
      incident: "Incident",
      incident_desc: "Report, track, and manage incident documentation",
      admin_site: "Admin Site",
      admin_site_desc: "System administration and configuration management",
      organization_admin: "Organization Admin",
      organization_admin_desc:
        "Organization administration and user management",
      profile: "Profile",
      logout: "Logout",
      toggle_theme: "Toggle theme",
    },
  },
  es: {
    translation: {
      home_portal: "Portal de Inicio",
      application: "Aplicación",
      admin: "Administrador",
      navigation: "Navegación",
      application_dashboard: "Panel de Aplicación",
      admin_dashboard: "Panel de Administrador",
      application_modules: "Módulos de Aplicación",
      administration: "Administración",
      dispatch: "Despacho",
      dispatch_desc:
        "Gestionar operaciones de despacho de emergencia y asignación de recursos",
      "911": "911",
      "911_desc":
        "Manejar llamadas de emergencia y coordinar esfuerzos de respuesta",
      jail: "Cárcel",
      jail_desc:
        "Gestión de instalaciones correccionales y seguimiento de reclusos",
      incident: "Incidente",
      incident_desc:
        "Reportar, rastrear y gestionar documentación de incidentes",
      admin_site: "Sitio de Administrador",
      admin_site_desc: "Administración del sistema y gestión de configuración",
      organization_admin: "Administración de la Organización",
      organization_admin_desc:
        "Administración de la organización y gestión de usuarios",
      profile: "Perfil",
      logout: "Cerrar Sesión",
      toggle_theme: "Cambiar tema",
    },
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: "en",
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
