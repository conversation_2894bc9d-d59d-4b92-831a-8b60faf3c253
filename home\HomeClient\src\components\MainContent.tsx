import { useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw } from "lucide-react";
import { useDecodedJwt } from "@/hooks/useDecodedJwt";
import { ModuleCard } from "./ModuleCard";


interface MainContentProps {
  searchTerm: string;
  view: "grid" | "list";
}

// Emoji icons for apps
const appIcons: Record<string, string> = {
  "dispatch": "📡",
  "call911": "🚨",
  "jail": "🏢",
  "incident": "📋",
  "admin": "⚙️",
  "relativity admin": "⚙️",
  "organization admin": "🏢",
};

const appColors: Record<string, string> = {
  "dispatch": "bg-blue-600",
  "call911": "bg-red-700",
  "jail": "bg-purple-600",
  "incident": "bg-orange-600",
  "admin": "bg-red-600",
  "relativity admin": "bg-red-600",
  "organization admin": "bg-purple-600",
};

export function MainContent({ searchTerm, view }: MainContentProps) {
  const jwtPayload = useDecodedJwt();
  const allowedApps: any[] =
    jwtPayload?.allowedApplication || jwtPayload?.allowedApplications || [];

  // Map allowedApps to UI app objects
  const apps = useMemo(() => {
    return allowedApps.map((app) => {
      const name = app.displayName || app.applicationName || "";
      const key = name.toLowerCase();
      let icon = appIcons[key] || appIcons[app.applicationName?.toLowerCase() || ""] || "📦";
      let color = appColors[key] || appColors[app.applicationName?.toLowerCase() || ""] || "bg-blue-500";
      let category = app.applicationName?.toLowerCase().includes("admin") ? "Admin" : "Application";
      return {
        id: app.applicationName,
        name,
        icon,
        color,
        description: app.description || "",
        category,
        redirectUrl: app.redirectUrl,
      };
    });
  }, [allowedApps]);

  // Filter by search term
  const filteredApps = useMemo(() => {
    return apps.filter(app =>
      app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [apps, searchTerm]);

  // Get categories present in filtered apps, with Admin first
  const categories = useMemo(() => {
    const unique = [...new Set(filteredApps.map(app => app.category))];
    // Sort so that 'Admin' comes first, then the rest
    return unique.sort((a, b) => {
      if (a === "Admin") return -1;
      if (b === "Admin") return 1;
      return a.localeCompare(b);
    });
  }, [filteredApps]);

  // UI rendering
  return (
    <div className="space-y-6 bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 rounded-xl p-6">


      {/* Category Sections */}
      {categories.map(category => {
        let categoryApps = filteredApps.filter(app => app.category === category);
        if (categoryApps.length === 0) return null;
        // For Admin category, sort so that 'relativity admin' is first
        if (category === "Admin") {
          categoryApps = [...categoryApps].sort((a, b) => {
            if (a.name.toLowerCase() === "relativity admin") return -1;
            if (b.name.toLowerCase() === "relativity admin") return 1;
            return a.name.localeCompare(b.name);
          });
        }
        return (
          <div key={category} className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 tracking-tight">{category}</h2>
              {/* <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 dark:text-gray-300"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button> */}
            </div>
            {view === "grid" ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {categoryApps.map(app => (
                  <ModuleCard key={app.id} module={app} />
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {categoryApps.map(app => (
                  <ModuleCard key={app.id} module={app} listView />
                ))}
              </div>
            )}
          </div>
        );
      })}

      {/* Add More Apps (optional) */}
      {/* <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
        <Button variant="outline" className="text-gray-600 hover:text-blue-600">
          <Plus className="h-4 w-4 mr-2" />
          Add more apps
        </Button>
        <p className="text-sm text-gray-500 mt-2">
          Discover and install new apps from the Microsoft Store
        </p>
      </div> */}

      {filteredApps.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-300">No Applications Found</p>
        </div>
      )}
    </div>
  );
}
