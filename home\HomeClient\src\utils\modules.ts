import { Radio, Phone, Building, FileText, Settings } from "lucide-react";

export const applicationModules = [
    {
        id: "dispatch",
        titleKey: "Dispatch",
        descriptionKey: "dispatch_desc",
        icon: Radio,
        color: "bg-blue-500",
        access: true,
    },
    {
        id: "call911",
        titleKey: "Call 911",
        descriptionKey: "911_desc",
        icon: Phone,
        color: "bg-red-500",
        access: true,
    },
    {
        id: "jail",
        titleKey: "Jail",
        descriptionKey: "jail_desc",
        icon: Building,
        color: "bg-orange-500",
        access: true,
    },
    {
        id: "incident",
        titleKey: "Incident",
        descriptionKey: "incident_desc",
        icon: FileText,
        color: "bg-green-500",
        access: true,
    },
];

export const adminModules = [
    {
        id: "relativityadmin",
        titleKey: "admin_site",
        descriptionKey: "admin_site_desc",
        icon: Settings,
        color: "bg-purple-500",
        access: true,
    },
    {
        id: "organizationadmin",
        titleKey: "organization_admin",
        descriptionKey: "organization_admin_desc",
        icon: Settings,
        color: "bg-pink-500",
        access: true,
    },
];
