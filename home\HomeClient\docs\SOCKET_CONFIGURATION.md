# Socket.IO Configuration After Authentication

This document explains how Socket.IO is configured after successful authentication in the HomeClient application.

## Overview

The application uses a layered approach for Socket.IO integration:

1. **`useAuth`** - Handles authentication state and token management
2. **`useSocket`** - Manages Socket.IO connection and basic events
3. **`useAuthWithSocket`** - Combines auth and socket with enhanced configuration

## Enhanced Socket Configuration

### Key Features

The `useAuthWithSocket` hook provides:

- **Automatic Socket Configuration**: Sets up event listeners after authentication success
- **Enhanced Event Handling**: Comprehensive event listeners for auth, notifications, and system messages
- **Authentication-aware Emit**: Ensures socket events are only emitted when authenticated
- **Graceful Logout**: Properly cleans up socket connections during logout
- **Connection Status Tracking**: Monitors socket configuration state

### Socket Events Configured After Authentication

#### Authentication Events
- `auth_expired` - Handles token expiration
- `force_logout` - Handles admin-initiated logout
- `session_timeout` - Handles session timeout due to inactivity

#### Application Events
- `notification` - Displays toast notifications (success, error, warning)
- `system_message` - Shows system-wide messages
- `user_status_update` - Handles user presence updates

#### Connection Events
- `connection_quality` - Monitors connection quality

### Environment Configuration

Add the following to your `.env` file:

```env
# Socket.IO Server URL
VITE_SOCKET=http://localhost:8081

# For production
# VITE_SOCKET=https://your-socket-server.com
```

### Usage Examples

#### Basic Usage

```typescript
import { useAuthWithSocket } from "@/hooks/useAuthWithSocket";

const MyComponent = () => {
  const {
    isAuthenticated,
    isConnected,
    socketConfigured,
    isSocketReady,
    authenticatedEmit,
    logout
  } = useAuthWithSocket();

  const sendMessage = () => {
    const success = authenticatedEmit("chat_message", {
      message: "Hello, world!",
      timestamp: new Date().toISOString()
    });
    
    if (!success) {
      console.warn("Failed to send message - not authenticated or connected");
    }
  };

  return (
    <div>
      <p>Socket Ready: {isSocketReady() ? "Yes" : "No"}</p>
      <button onClick={sendMessage} disabled={!isSocketReady()}>
        Send Message
      </button>
    </div>
  );
};
```

#### Advanced Event Handling

```typescript
import { useAuthWithSocket } from "@/hooks/useAuthWithSocket";
import { useEffect } from "react";

const ChatComponent = () => {
  const { socket, isSocketReady, authenticatedEmit } = useAuthWithSocket();

  useEffect(() => {
    if (!isSocketReady()) return;

    // Custom event listeners
    const handleChatMessage = (data: any) => {
      console.log("New chat message:", data);
    };

    const handleUserJoined = (data: any) => {
      console.log("User joined:", data);
    };

    // Add listeners
    socket.on("chat_message", handleChatMessage);
    socket.on("user_joined", handleUserJoined);

    // Cleanup
    return () => {
      socket.off("chat_message", handleChatMessage);
      socket.off("user_joined", handleUserJoined);
    };
  }, [isSocketReady, socket]);

  return <div>Chat Component</div>;
};
```

### Socket Status Component

Use the provided `SocketStatus` component to monitor connection status:

```typescript
import { SocketStatus } from "@/components/SocketStatus";

const Dashboard = () => {
  return (
    <div>
      <h1>Dashboard</h1>
      <SocketStatus />
    </div>
  );
};
```

### Configuration Flow

1. **Authentication Success**: User logs in and receives access token
2. **Socket Connection**: `useSocket` establishes connection with auth token
3. **Event Configuration**: `useAuthWithSocket` sets up additional event listeners
4. **Ready State**: Socket is ready for application-specific communication

### Error Handling

The hook handles various error scenarios:

- **Authentication Expiry**: Automatically logs out user and shows notification
- **Connection Errors**: Displays connection quality warnings
- **Socket Logout Failures**: Continues with regular logout if socket logout fails

### Best Practices

1. **Always check `isSocketReady()`** before emitting events
2. **Use `authenticatedEmit()`** instead of direct socket emit
3. **Clean up event listeners** in useEffect cleanup functions
4. **Handle connection state changes** gracefully in your components
5. **Test socket functionality** with the provided SocketStatus component

### Troubleshooting

#### Common Issues

1. **Socket not connecting**: Check VITE_SOCKET environment variable
2. **Events not configured**: Ensure user is authenticated and socket is connected
3. **Emit failures**: Verify authentication state and connection status

#### Debug Information

The hook provides extensive console logging:
- `🔧 Configuring Socket.IO after authentication success...`
- `✅ Socket.IO events configured successfully`
- `🔔 [Event] received: [data]`

### Security Considerations

- Socket connection uses JWT token for authentication
- All socket events are validated on the server side
- Automatic logout on authentication expiry prevents unauthorized access
- Connection quality monitoring helps detect potential security issues
