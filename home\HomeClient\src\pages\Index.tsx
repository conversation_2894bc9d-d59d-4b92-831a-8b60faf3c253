import { Home } from "@/components/Home";

const Index = () => {


  // Initialize integrated auth and socket connection
  const { socket, isConnected, connectionError, on, off, logout } = useAuthWithSocket();

  useEffect(() => {
    // Set up any additional socket event listeners here
    if (socket && isConnected) {
      // Example: Listen for notifications
      const handleNotification = (data: any) => {
        console.log("Received notification:", data);
        // Handle notification logic here
      };

      on("notification", handleNotification);

      // Cleanup listeners on unmount
      return () => {
        off("notification", handleNotification);
      };
    }
  }, [socket, isConnected, on, off]);

  // Log connection status for debugging
  useEffect(() => {
    if (connectionError) {
      console.error("Socket connection error:", connectionError);
    }
  }, [connectionError]);

  // The logout function from useAuthWithSocket already integrates socket and auth logout
  // You can now use the 'logout' function anywhere in your app for integrated logout
  return <Home />;
};

export default Index;
