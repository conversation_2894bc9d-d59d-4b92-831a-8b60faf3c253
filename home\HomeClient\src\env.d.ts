/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_ADMIN_URL?: string;
  readonly VITE_LOGIN_URL?: string;
  readonly VITE_DISPATCH_URL?: string;
  readonly VITE_INCIDENT_URL?: string;
  readonly VITE_CALL911_URL?: string;
  readonly VITE_DASHBOARD_URL?: string;
  readonly VITE_LOCALHOST?: string;
  readonly VITE_SOCKET?: string;
  readonly VITE_LOGIN_API_BASE_URL?: string;
  readonly VITE_ORGANIZATION_ADMIN_URL?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
