import { useAuth } from "@/hooks/useAuth";
import { useSocket } from "@/hooks/useSocket";
import { useEffect, useCallback, useState } from "react";
import { toast } from "@/components/ui/sonner";

/**
 * Custom hook that combines authentication and socket functionality
 * Provides an integrated logout that handles both socket and auth logout
 * Includes additional Socket.IO configuration after authentication success
 */
export const useAuthWithSocket = () => {
  const auth = useAuth();
  const socket = useSocket();
  const [socketConfigured, setSocketConfigured] = useState(false);

  // Configure Socket.IO after authentication success
  useEffect(() => {
    if (auth.isAuthenticated && socket.isConnected && !socketConfigured) {
      console.log("🔧 Configuring Socket.IO after authentication success...");

      // Set up additional socket event listeners after authentication
      configureSocketEvents();
      setSocketConfigured(true);
    } else if (!auth.isAuthenticated || !socket.isConnected) {
      setSocketConfigured(false);
    }
  }, [auth.isAuthenticated, socket.isConnected, socketConfigured]);

  // Configure additional socket event listeners
  const configureSocketEvents = useCallback(() => {
    if (!socket.socket) return;

    // Listen for authentication-related events
    socket.on("auth_expired", handleAuthExpired);
    socket.on("force_logout", handleForceLogout);
    socket.on("session_timeout", handleSessionTimeout);

    // Listen for application-specific events
    socket.on("notification", handleNotification);
    socket.on("system_message", handleSystemMessage);
    socket.on("user_status_update", handleUserStatusUpdate);

    // Listen for connection quality events
    socket.on("connection_quality", handleConnectionQuality);

    console.log("✅ Socket.IO events configured successfully");
  }, [socket]);

  // Event handlers
  const handleAuthExpired = useCallback((data: any) => {
    console.log("🔔 Authentication expired:", data);
    toast.error("Your session has expired. Please log in again.");
    logout();
  }, []);

  const handleForceLogout = useCallback((data: any) => {
    console.log("🔔 Force logout received:", data);
    toast.warning(data.message || "You have been logged out by an administrator.");
    logout();
  }, []);

  const handleSessionTimeout = useCallback((data: any) => {
    console.log("🔔 Session timeout:", data);
    toast.warning("Your session has timed out due to inactivity.");
    logout();
  }, []);

  const handleNotification = useCallback((data: any) => {
    console.log("🔔 Notification received:", data);
    if (data.type === "success") {
      toast.success(data.message);
    } else if (data.type === "error") {
      toast.error(data.message);
    } else if (data.type === "warning") {
      toast.warning(data.message);
    } else {
      toast(data.message);
    }
  }, []);

  const handleSystemMessage = useCallback((data: any) => {
    console.log("🔔 System message received:", data);
    toast(data.message, {
      description: data.description,
      duration: data.duration || 5000,
    });
  }, []);

  const handleUserStatusUpdate = useCallback((data: any) => {
    console.log("🔔 User status update:", data);
    // Handle user status updates (online/offline, etc.)
    // This can be used to update UI components showing user presence
  }, []);

  const handleConnectionQuality = useCallback((data: any) => {
    console.log("🔔 Connection quality update:", data);
    if (data.quality === "poor") {
      toast.warning("Poor connection quality detected. Some features may be limited.");
    }
  }, []);

  // Enhanced logout function that uses socket logout first, then auth logout
  const logout = async () => {
    try {
      // First, perform socket logout if connected
      if (socket.isConnected && socket.logout) {
        console.log("🔌 Performing socket logout...");
        await socket.logout();
      }
    } catch (error) {
      console.error("❌ Socket logout failed:", error);
      // Continue with regular logout even if socket logout fails
    }

    // Clean up socket event listeners
    if (socket.socket) {
      socket.off("auth_expired");
      socket.off("force_logout");
      socket.off("session_timeout");
      socket.off("notification");
      socket.off("system_message");
      socket.off("user_status_update");
      socket.off("connection_quality");
    }

    // Then perform regular auth logout
    try {
      await auth.logout();
    } catch (error) {
      console.error("❌ Auth logout failed:", error);
      throw error;
    }
  };

  // Enhanced socket emit with authentication check
  const authenticatedEmit = useCallback((event: string, data?: any) => {
    if (!auth.isAuthenticated) {
      console.warn("❌ Cannot emit socket event: User not authenticated");
      return false;
    }

    if (!socket.isConnected) {
      console.warn("❌ Cannot emit socket event: Socket not connected");
      return false;
    }

    socket.emit(event, data);
    return true;
  }, [auth.isAuthenticated, socket.isConnected, socket.emit]);

  // Helper function to check if socket is ready for use
  const isSocketReady = useCallback(() => {
    return auth.isAuthenticated && socket.isConnected && socketConfigured;
  }, [auth.isAuthenticated, socket.isConnected, socketConfigured]);

  return {
    ...auth,
    ...socket,
    logout, // Override logout with enhanced version
    authenticatedEmit, // Enhanced emit function
    isSocketReady, // Helper to check socket readiness
    socketConfigured, // Configuration status
  };
};

export default useAuthWithSocket;
