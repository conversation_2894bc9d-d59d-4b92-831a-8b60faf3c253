import React from "react";
import { useAuthWithSocket } from "@/hooks/useAuthWithSocket";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Wifi, WifiOff, User, LogOut } from "lucide-react";

/**
 * Component to display Socket.IO connection status and provide socket controls
 * Demonstrates usage of the enhanced useAuthWithSocket hook
 */
export const SocketStatus: React.FC = () => {
  const {
    isAuthenticated,
    user,
    isConnected,
    connectionError,
    socketConfigured,
    isSocketReady,
    authenticatedEmit,
    logout,
    reconnect,
  } = useAuthWithSocket();

  const handleTestEmit = () => {
    const success = authenticatedEmit("test_event", {
      message: "Test message from client",
      timestamp: new Date().toISOString(),
    });
    
    if (success) {
      console.log("✅ Test event emitted successfully");
    }
  };

  const handlePing = () => {
    const success = authenticatedEmit("ping", { timestamp: Date.now() });
    if (success) {
      console.log("🏓 Ping sent to server");
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isConnected ? (
            <Wifi className="h-5 w-5 text-green-500" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-500" />
          )}
          Socket Status
        </CardTitle>
        <CardDescription>
          Real-time connection status and controls
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Authentication Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Authentication:</span>
          <Badge variant={isAuthenticated ? "default" : "destructive"}>
            {isAuthenticated ? "Authenticated" : "Not Authenticated"}
          </Badge>
        </div>

        {/* User Info */}
        {isAuthenticated && user && (
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="text-sm">{user.username || user.email}</span>
          </div>
        )}

        {/* Socket Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Socket Connection:</span>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
        </div>

        {/* Socket Configuration Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Socket Configured:</span>
          <Badge variant={socketConfigured ? "default" : "secondary"}>
            {socketConfigured ? "Yes" : "No"}
          </Badge>
        </div>

        {/* Socket Ready Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Socket Ready:</span>
          <Badge variant={isSocketReady() ? "default" : "secondary"}>
            {isSocketReady() ? "Ready" : "Not Ready"}
          </Badge>
        </div>

        {/* Connection Error */}
        {connectionError && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            <strong>Connection Error:</strong> {connectionError}
          </div>
        )}

        {/* Controls */}
        <div className="space-y-2">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleTestEmit}
              disabled={!isSocketReady()}
            >
              Test Emit
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handlePing}
              disabled={!isSocketReady()}
            >
              Ping
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={reconnect}
              disabled={isConnected}
            >
              Reconnect
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={logout}
              className="flex items-center gap-1"
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>

        {/* Status Summary */}
        <div className="pt-2 border-t text-xs text-gray-500">
          {isSocketReady() ? (
            "✅ Socket.IO is ready for real-time communication"
          ) : (
            "⏳ Waiting for authentication and socket connection..."
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SocketStatus;
